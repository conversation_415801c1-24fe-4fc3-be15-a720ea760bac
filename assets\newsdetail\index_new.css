/* 页面基础样式 */
.page {
	min-height: 100vh;
	background-color: #ffffff;
	display: flex;
	flex-direction: column;
}

/* 主导航栏 */
.main-header {
	background-color: #ffffff;
	border-bottom: 1px solid #e5e5e5;
}

.header-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 20px 24px;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.logo-section {
	display: flex;
	align-items: center;
	gap: 12px;
}

.logo-icon {
	width: 42px;
	height: 42px;
	border-radius: 50%;
	background-image: url(./img/e6475b5c45ac4b46a9ee3028e79f8323_mergeImage.png);
	background-size: cover;
	background-position: center;
	border: 1px solid #979797;
}

.company-name {
	font-size: 30px;
	font-family: "Alibaba-PuHuiTi-M", sans-serif;
	font-weight: 500;
	color: #000000;
	margin: 0;
}

.main-nav {
	display: flex;
	align-items: center;
	gap: 40px;
}

.nav-link {
	font-size: 16px;
	font-family: "Alibaba-PuHuiTi-R", sans-serif;
	color: #000000;
	text-decoration: none;
	transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
	color: #0055c3;
}

.nav-icon {
	width: 14px;
	height: 14px;
}

/* 二级导航栏 */
.secondary-nav {
	background-color: #090909;
}

.secondary-nav-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 24px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 92px;
}

.product-nav {
	display: flex;
	align-items: center;
	gap: 72px;
}

.product-link {
	font-size: 16px;
	font-family: "Alibaba-PuHuiTi-R", sans-serif;
	color: #ffffff;
	text-decoration: none;
	transition: color 0.3s ease;
}

.product-link:hover {
	color: #cccccc;
}

.contact-btn {
	background-color: #ffffff;
	color: #000000;
	font-size: 16px;
	font-family: "Alibaba-PuHuiTi-R", sans-serif;
	padding: 35px 30px;
	border: none;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

.contact-btn:hover {
	background-color: #f5f5f5;
}

/* 面包屑导航 */
.breadcrumb {
	background-color: #ffffff;
	padding: 12px 0;
}

.breadcrumb-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 24px;
	display: flex;
	align-items: center;
	gap: 6px;
}

.breadcrumb-link {
	font-size: 14px;
	font-family: "PingFangSC-Regular", sans-serif;
	color: rgba(0, 0, 0, 0.6);
	text-decoration: none;
	transition: color 0.3s ease;
}

.breadcrumb-link:hover {
	color: #0055c3;
}

.breadcrumb-arrow {
	width: 18px;
	height: 18px;
}

.breadcrumb-current {
	font-size: 14px;
	font-family: "PingFangSC-Medium", sans-serif;
	font-weight: 500;
	color: #0055c3;
}

/* 主要内容区域 */
.main-content {
	flex: 1;
	background-color: #ffffff;
}

.content-container {
	max-width: 1440px;
	margin: 0 auto;
	padding: 0 24px;
}

/* 新闻头图 */
.news-hero-image {
	width: 100%;
	height: 400px;
	background-image: url(./img/4d22bf0da8ec4b43bff29e393012835c_mergeImage.png);
	background-size: cover;
	background-position: center;
	border-radius: 8px;
	margin: 12px 0 24px 0;
}

/* 新闻元信息 */
.news-meta {
	display: flex;
	align-items: center;
	gap: 16px;
	margin-bottom: 20px;
}

.news-category {
	font-size: 14px;
	font-family: "PingFangSC-Regular", sans-serif;
	color: rgba(0, 0, 0, 0.8);
	letter-spacing: 1.3px;
}

.news-date {
	font-size: 14px;
	font-family: "PingFangSC-Regular", sans-serif;
	color: rgba(0, 0, 0, 0.8);
	letter-spacing: 1.3px;
}

/* 新闻标题 */
.news-title {
	font-size: 36px;
	font-family: "PingFangSC-Regular", sans-serif;
	color: rgba(0, 0, 0, 0.8);
	line-height: 1.4;
	margin: 0 0 20px 0;
}

/* 新闻副标题 */
.news-subtitle {
	font-size: 18px;
	font-family: "PingFangSC-Regular", sans-serif;
	color: rgba(0, 0, 0, 0.8);
	margin: 0 0 32px 0;
}

/* 新闻正文 */
.news-article {
	line-height: 1.6;
}

.news-paragraph {
	font-size: 18px;
	font-family: "PingFangSC-Regular", sans-serif;
	color: rgba(0, 0, 0, 0.8);
	line-height: 1.6;
	margin: 0 0 24px 0;
}

.section-title {
	font-size: 18px;
	font-family: "PingFangSC-Medium", sans-serif;
	font-weight: 500;
	color: rgba(0, 0, 0, 0.8);
	margin: 32px 0 24px 0;
}

.product-section {
	background-color: #f8f9fa;
	padding: 24px;
	border-radius: 8px;
	margin: 24px 0;
}

.product-title {
	font-size: 18px;
	font-family: "PingFangSC-Medium", sans-serif;
	font-weight: 500;
	color: rgba(0, 0, 0, 0.8);
	margin: 0 0 16px 0;
}

.product-description {
	font-size: 18px;
	font-family: "PingFangSC-Regular", sans-serif;
	color: rgba(0, 0, 0, 0.8);
	line-height: 1.6;
	margin: 0;
}

.contact-title {
	font-size: 18px;
	font-family: "PingFangSC-Medium", sans-serif;
	font-weight: 500;
	color: rgba(0, 0, 0, 0.8);
	margin: 32px 0 16px 0;
}

.contact-info {
	font-size: 18px;
	font-family: "PingFangSC-Regular", sans-serif;
	color: rgba(0, 0, 0, 0.8);
	line-height: 1.6;
	margin: 0 0 24px 0;
}

.company-description {
	font-size: 18px;
	font-family: "PingFangSC-Regular", sans-serif;
	color: rgba(0, 0, 0, 0.8);
	line-height: 1.6;
	margin: 0 0 60px 0;
}

/* 相关图片展示 */
.related-images {
	background-color: #ffffff;
	padding: 60px 0 0 0;
}

.image-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 24px;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.related-image {
	width: 402px;
	height: 164px;
	background-image: url(./img/70f8f0b4e20d4a2c8255d447c102f999_mergeImage.png);
	background-size: cover;
	background-position: center;
	border-radius: 8px;
}

.image-divider {
	width: 402px;
	height: 4px;
	background-color: #ec2914;
	margin-top: 0;
}

/* 页脚 */
.main-footer {
	background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
		center/cover;
	margin-top: 120px;
	padding: 40px 0;
}

.footer-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 24px;
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
}

.footer-content {
	display: flex;
	gap: 120px;
}

.footer-section {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.footer-title {
	font-size: 16px;
	font-family: "Alibaba-PuHuiTi-B", sans-serif;
	font-weight: bold;
	color: #ffffff;
	margin: 0 0 8px 0;
}

.footer-link {
	font-size: 16px;
	font-family: "AlibabaPuHuiTiR", sans-serif;
	color: #ffffff;
	text-decoration: none;
	transition: color 0.3s ease;
}

.footer-link:hover {
	color: #cccccc;
}

.footer-logo {
	display: flex;
	align-items: center;
	gap: 12px;
}

.footer-logo-image {
	width: 56px;
	height: 55px;
}

.footer-company-name {
	font-size: 30px;
	font-family: "Alibaba-PuHuiTi-M", sans-serif;
	font-weight: 500;
	color: #000000;
}

/* 响应式设计 */
@media (max-width: 1200px) {
	.header-container,
	.secondary-nav-container,
	.breadcrumb-container,
	.content-container,
	.image-container,
	.footer-container {
		padding-left: 16px;
		padding-right: 16px;
	}
}

@media (max-width: 900px) {
	.header-container {
		flex-direction: column;
		gap: 20px;
	}

	.main-nav {
		gap: 24px;
	}

	.secondary-nav-container {
		flex-direction: column;
		height: auto;
		padding: 20px 16px;
		gap: 20px;
	}

	.product-nav {
		gap: 32px;
	}

	.footer-content {
		gap: 60px;
	}

	.news-title {
		font-size: 28px;
	}

	.related-image,
	.image-divider {
		width: 100%;
		max-width: 402px;
	}
}

@media (max-width: 600px) {
	.header-container {
		padding: 16px;
	}

	.main-nav {
		flex-wrap: wrap;
		gap: 16px;
		justify-content: center;
	}

	.secondary-nav-container {
		padding: 16px;
	}

	.product-nav {
		flex-wrap: wrap;
		gap: 16px;
		justify-content: center;
	}

	.breadcrumb-container {
		flex-wrap: wrap;
	}

	.news-title {
		font-size: 24px;
	}

	.news-paragraph,
	.product-description,
	.contact-info,
	.company-description {
		font-size: 16px;
	}

	.footer-container {
		flex-direction: column;
		gap: 40px;
	}

	.footer-content {
		flex-direction: column;
		gap: 32px;
	}
}
