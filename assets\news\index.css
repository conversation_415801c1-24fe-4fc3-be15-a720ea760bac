/* 新闻页面样式 - 基于招聘页面样式 */

/* 全局重置 */
* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

body {
	font-family: "AlibabaPuHuiTi-Regular", -apple-system, BlinkMacSystemFont,
		"Segoe UI", Robot<PERSON>, sans-serif;
	line-height: 1.6;
	color: #333;
	background-color: #fff;
}

/* 导航栏样式 - 新闻页面 */
.company-name {
	color: #ffffff !important;
}

.page {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

.main-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}

/* 新闻页面头部区域 */
.hero-section {
	background-image: url(./img/15e8b85ed4654c07b1c1d7c677702da8_mergeImage.png);
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	min-height: 520px;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 5%;
	position: relative;
}

.hero-content {
	text-align: right;
	color: white;
	max-width: 600px;
	transform: translate(240px, 85px);
}

.hero-title {
	font-size: 3rem;
	font-family: "SourceHanSansCN-Regular", serif;
	font-weight: 400;
	margin-bottom: 0.5rem;
	line-height: 1.2;
}

.hero-subtitle {
	font-size: 1.5rem;
	font-family: "AlibabaPuHuiTi-Light", sans-serif;
	font-weight: 300;
	opacity: 0.9;
}

/* 智诚新闻订阅区域包装器 */
.news-subscription-wrapper {
	width: 1025px;
	margin: 0 auto;
	background-color: transparent;
	position: relative;
}

/* 智诚新闻订阅区域 */
.news-subscription-section {
	position: relative;
	background-color: #000000;
	height: 184px;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	padding: 40px 0;
	margin-top: -150px;
	width: 567px;
	z-index: 2;
	clip-path: polygon(0 0, 100% 0, 100% 100%, 30px 100%, 0 calc(100% - 33px));
	border-top: 4px solid #ec2914;
}

.news-subscription-content {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	width: 450px;
	height: 104px;
	margin-left: 58px;
}

.subscription-title {
	width: 144px;
	color: rgba(255, 255, 255, 1);
	font-size: 36px;
	font-family: "SourceHanSansCN-Regular", serif;
	font-weight: normal;
	text-align: left;
	white-space: nowrap;
	line-height: 54px;
	margin: 0;
}

.subscription-description {
	width: 450px;
	color: rgba(255, 255, 255, 1);
	font-size: 20px;
	font-family: "AlibabaPuHuiTi-Light", sans-serif;
	font-weight: 200;
	text-align: left;
	line-height: 27px;
	margin: 14px 0 0 0;
}

/* 新闻信息主体区域 */
.news-section {
	padding: 3rem 0;
	background-color: #fff;
}

.news-container {
	max-width: 1200px;
	margin: 0 auto;
}

.news-description {
	font-size: 1.5rem;
	font-family: "AlibabaPuHuiTi-Light", sans-serif;
	font-weight: 300;
	/* text-align: center; */
	margin: 0 auto 82px;
	line-height: 1.4;
	color: #333;
	width: 870px;
}

/* 搜索筛选区域 */
.search-filters {
	display: flex;
	gap: 1.5rem;
	margin-bottom: 3rem;
	flex-wrap: wrap;
	align-items: center;
	justify-content: space-between;
}

.filters-left {
	display: flex;
	gap: 1.5rem;
	flex-wrap: wrap;
	align-items: center;
}

.filters-right {
	display: flex;
	align-items: center;
}

.filter-group {
	position: relative;
}

.filter-select {
	padding: 0.75rem 2.5rem 0.75rem 0.75rem;
	border: 1px solid #000;
	background-color: #fff;
	font-size: 0.875rem;
	font-family: inherit;
	cursor: pointer;
	appearance: none;
	min-width: 120px;
	transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.filter-select:hover {
	border-color: #666;
}

.filter-select:focus {
	outline: none;
	border-color: #0066cc;
	box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
}

.filter-group::after {
	content: "";
	position: absolute;
	right: 0.75rem;
	top: 50%;
	transform: translateY(-50%);
	width: 0;
	height: 0;
	border-left: 5px solid transparent;
	border-right: 5px solid transparent;
	border-top: 6px solid #333;
	pointer-events: none;
}

.search-group {
	display: flex;
	background-color: #fff;
	border: none;
	box-shadow: 0 0 10px 1px rgba(0, 0, 0, 0.2);
	border-radius: 4px;
	overflow: hidden;
	width: 400px;
	min-width: 300px;
}

.search-input {
	flex: 1;
	padding: 0.75rem;
	border: none;
	font-size: 0.875rem;
	font-family: inherit;
	color: #333;
}

.search-input::placeholder {
	color: #aaa;
}

.search-input:focus {
	outline: none;
}

.search-btn {
	padding: 0.75rem 1rem;
	background: none;
	border: none;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: background-color 0.3s ease;
}

.search-btn:hover {
	background-color: #f5f5f5;
}

.search-btn img {
	width: 14px;
	height: 14px;
}

/* 新闻表格表头 */
.news-table-header {
	display: grid;
	grid-template-columns: 1fr 1fr 2fr 60px;
	gap: 1rem;
	padding: 1rem;
	background-color: #f8f9fa;
	border-radius: 8px 8px 0 0;
	font-weight: 600;
	font-size: 1rem;
	color: #333;
	margin-bottom: 0.5rem;
}

.header-cell {
	display: flex;
	align-items: center;
	justify-content: space-between;
	cursor: pointer;
	transition: color 0.3s ease;
}

.header-cell:hover {
	color: #0066cc;
}

.header-cell img {
	width: 8px;
	height: 12px;
	margin-left: 0.5rem;
	transition: transform 0.3s ease;
}

.header-cell:hover img {
	transform: scale(1.1);
}

/* 新闻列表 */
.news-list {
	background-color: #fff;
	border-radius: 0 0 8px 8px;
	overflow: hidden;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.news-item {
	display: grid;
	grid-template-columns: 1fr 1fr 2fr 60px;
	gap: 1rem;
	padding: 1.25rem 1rem;
	border-bottom: 1px solid #e9ecef;
	transition: background-color 0.3s ease, transform 0.2s ease;
	align-items: center;
}

.news-item:last-child {
	border-bottom: none;
}

.news-item:hover {
	background: rgba(0, 85, 195, 0.8);
	transform: translateY(-1px);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.news-item:hover .news-cell {
	color: #fff;
}

.news-cell {
	font-size: 1rem;
	color: #333;
	line-height: 1.4;
}

.category-col {
	font-weight: 500;
	color: #333;
}

.title-col {
	font-weight: 500;
}

.date-col {
	color: #666;
	font-size: 0.9rem;
}

.view-btn {
	width: 40px;
	height: 40px;
	border: none;
	background-color: #f8f9fa;
	border-radius: 50%;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.view-btn:hover {
	background-color: #0066cc;
	transform: scale(1.1);
}

.view-btn img {
	width: 14px;
	height: 14px;
	transition: filter 0.3s ease;
}

.view-btn:hover img {
	filter: brightness(0) invert(1);
}

/* 加载更多按钮 */
.load-more-section {
	display: flex;
	justify-content: center;
	margin-top: 2rem;
}

.load-more-btn {
	padding: 0.75rem 2rem;
	background-color: #000;
	color: #fff;
	border: none;
	border-radius: 4px;
	font-size: 1rem;
	font-family: inherit;
	cursor: pointer;
	transition: all 0.3s ease;
}

.load-more-btn:hover {
	background-color: #333;
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 响应式设计 */
@media (max-width: 1200px) {
	.news-container {
		max-width: 100%;
	}

	.hero-title {
		font-size: 2.5rem;
	}

	.hero-subtitle {
		font-size: 1.25rem;
	}

	.news-description {
		font-size: 1.25rem;
	}

	.search-group {
		width: 350px;
		min-width: 250px;
	}

	.news-subscription-wrapper {
		width: 100%;
		max-width: 1025px;
		margin: 0 20px;
	}

	.news-subscription-section {
		max-width: 100%;
		margin: -150px auto 0;
	}

	.subscription-title {
		font-size: 32px;
	}

	.subscription-description {
		font-size: 18px;
		width: 400px;
	}
}

@media (max-width: 900px) {
	.hero-section {
		min-height: 400px;
		justify-content: center;
		text-align: center;
	}

	.hero-content {
		text-align: center;
		transform: translateX(100px); /* 适配移动端的位置调整 */
	}

	.hero-title {
		font-size: 2rem;
	}

	.hero-subtitle {
		font-size: 1.125rem;
	}

	.search-filters {
		flex-direction: column;
		align-items: stretch;
		gap: 1rem;
	}

	.filters-left {
		justify-content: center;
	}

	.filters-right {
		justify-content: center;
	}

	.search-group {
		width: 100%;
		min-width: auto;
		max-width: 400px;
	}

	.news-subscription-wrapper {
		width: 100%;
		margin: 0 10px;
	}

	.news-subscription-section {
		margin: -150px auto 0;
		height: 150px;
	}

	.news-subscription-content {
		margin-left: 30px;
		width: 350px;
	}

	.subscription-title {
		font-size: 28px;
	}

	.subscription-description {
		font-size: 16px;
		width: 350px;
	}

	.news-table-header,
	.news-item {
		grid-template-columns: 1fr;
		gap: 0.5rem;
		text-align: left;
	}

	.news-table-header {
		display: none; /* 隐藏表头在移动端 */
	}

	.news-item {
		display: block;
		padding: 1.5rem;
		margin-bottom: 1rem;
		border-radius: 8px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		border-bottom: none;
	}

	.news-cell {
		margin-bottom: 0.5rem;
		display: block;
	}

	.news-cell::before {
		content: attr(data-label) ": ";
		font-weight: 600;
		color: #666;
		display: inline-block;
		min-width: 80px;
	}

	.view-btn {
		position: absolute;
		top: 1rem;
		right: 1rem;
	}

	.news-item {
		position: relative;
		padding-right: 4rem;
	}
}

@media (max-width: 600px) {
	.hero-section {
		min-height: 300px;
		padding: 0 1rem;
	}

	.hero-content {
		transform: translateX(0); /* 在小屏幕上居中显示 */
	}

	.hero-title {
		font-size: 1.75rem;
	}

	.hero-subtitle {
		font-size: 1rem;
	}

	.news-description {
		font-size: 1.125rem;
		margin-bottom: 2rem;
	}

	.search-filters {
		gap: 1rem;
	}

	.filter-select {
		padding: 0.625rem 2rem 0.625rem 0.625rem;
		font-size: 0.8125rem;
	}

	.search-input {
		padding: 0.625rem;
		font-size: 0.8125rem;
	}

	.search-btn {
		padding: 0.625rem 0.875rem;
	}

	.news-item {
		padding: 1.25rem;
		margin-bottom: 0.75rem;
	}

	.news-cell {
		font-size: 0.9rem;
	}

	.load-more-btn {
		padding: 0.625rem 1.5rem;
		font-size: 0.9rem;
	}

	.news-subscription-wrapper {
		width: 100%;
		margin: 0 5px;
	}

	.news-subscription-section {
		margin: -150px auto 0;
		height: 120px;
		padding: 20px 0;
	}

	.news-subscription-content {
		margin-left: 20px;
		width: 280px;
		height: auto;
	}

	.subscription-title {
		font-size: 24px;
		width: auto;
		height: auto;
		line-height: 1.2;
	}

	.subscription-description {
		font-size: 14px;
		width: 280px;
		height: auto;
		line-height: 1.4;
		margin-top: 10px;
	}
}
