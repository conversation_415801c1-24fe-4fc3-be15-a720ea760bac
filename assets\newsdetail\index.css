.page {
  position: relative;
  width: 1920px;
  height: 1934px;
  overflow: hidden;
}

.block_1 {
  background-color: rgba(255, 255, 255, 1);
  width: 1920px;
  height: 1934px;
}

.group_1 {
  width: 1453px;
  height: 92px;
}

.box_1 {
  border-radius: 50%;
  background-image: url(./img/e6475b5c45ac4b46a9ee3028e79f8323_mergeImage.png);
  width: 42px;
  height: 42px;
  border: 1px solid rgba(151, 151, 151, 1);
  margin-top: 23px;
}

.text_1 {
  width: 119px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: Alibaba-PuHuiTi-M;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
  margin: 24px 0 0 12px;
}

.box_2 {
  background-color: rgba(255, 255, 255, 1);
  width: 1240px;
  height: 92px;
  margin-left: 40px;
}

.text_2 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: Ali<PERSON>ba-PuHuiTi-R;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 36px 0 0 266px;
}

.text_3 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: Alibaba-PuHuiTi-R;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 36px 0 0 39px;
}

.text_4 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: Alibaba-PuHuiTi-R;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 36px 0 0 40px;
}

.text_5 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: Alibaba-PuHuiTi-R;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 36px 0 0 40px;
}

.text_6 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: Alibaba-PuHuiTi-R;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 36px 0 0 40px;
}

.thumbnail_1 {
  width: 14px;
  height: 14px;
  margin: 39px 446px 0 40px;
}

.group_2 {
  background-color: rgba(9, 9, 9, 1);
  width: 1478px;
  height: 92px;
  margin-left: 442px;
}

.text_7 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: Alibaba-PuHuiTi-R;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 36px 0 0 50px;
}

.text_8 {
  width: 48px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: Alibaba-PuHuiTi-R;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 36px 0 0 72px;
}

.text_9 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: Alibaba-PuHuiTi-R;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 36px 0 0 70px;
}

.text-wrapper_1 {
  background-color: rgba(255, 255, 255, 1);
  height: 92px;
  width: 122px;
  margin: 0 447px 0 574px;
}

.text_10 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: Alibaba-PuHuiTi-R;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 35px 0 0 30px;
}

.group_3 {
  width: 564px;
  height: 22px;
  margin: 12px 0 0 460px;
}

.text_11 {
  width: 84px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.thumbnail_2 {
  width: 18px;
  height: 18px;
  margin: 2px 0 0 6px;
}

.text_12 {
  width: 56px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.6);
  font-size: 14px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 6px;
}

.thumbnail_3 {
  width: 18px;
  height: 18px;
  margin: 2px 0 0 6px;
}

.text_13 {
  width: 364px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 85, 195, 1);
  font-size: 14px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 6px;
}

.group_4 {
  background-image: url(./img/4d22bf0da8ec4b43bff29e393012835c_mergeImage.png);
  width: 1440px;
  height: 400px;
  margin: 12px 0 0 240px;
}

.text_14 {
  width: 179px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.8);
  font-size: 14px;
  letter-spacing: 1.3125px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 6px 0 0 240px;
}

.text_15 {
  width: 936px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.8);
  font-size: 36px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 50px;
  margin: 20px 0 0 240px;
}

.text_16 {
  width: 253px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.8);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 20px 0 0 240px;
}

.text_17 {
  width: 1440px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.8);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 25px;
  margin: 16px 0 0 240px;
}

.text_18 {
  width: 1440px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.8);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 25px;
  margin: 16px 0 0 240px;
}

.text_19 {
  width: 162px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.8);
  font-size: 18px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 16px 0 0 240px;
}

.text-wrapper_2 {
  width: 1440px;
  height: 50px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 25px;
  margin: 16px 0 0 240px;
}

.text_20 {
  width: 1440px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.8);
  font-size: 18px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 25px;
}

.text_21 {
  width: 1440px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.8);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 25px;
}

.text-wrapper_3 {
  width: 1440px;
  height: 50px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 25px;
  margin: 16px 0 0 240px;
}

.text_22 {
  width: 1440px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.8);
  font-size: 18px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 25px;
}

.text_23 {
  width: 1440px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.8);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 25px;
}

.text-wrapper_4 {
  width: 1440px;
  height: 50px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 25px;
  margin: 16px 0 0 240px;
}

.text_24 {
  width: 1440px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.8);
  font-size: 18px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 25px;
}

.text_25 {
  width: 1440px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.8);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 25px;
}

.text_26 {
  width: 1440px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.8);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 25px;
  margin: 16px 0 0 240px;
}

.text_27 {
  width: 180px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.8);
  font-size: 18px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 16px 0 0 240px;
}

.text_28 {
  width: 867px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.8);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 16px 0 0 240px;
}

.text_29 {
  width: 1440px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.8);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: NaN;
  text-align: left;
  line-height: 25px;
  margin: 16px 0 0 240px;
}

.group_5 {
  background-image: url(./img/70f8f0b4e20d4a2c8255d447c102f999_mergeImage.png);
  width: 402px;
  height: 164px;
  margin: 60px 0 0 759px;
}

.group_6 {
  background-color: rgba(236, 41, 20, 1);
  width: 402px;
  height: 4px;
  margin-left: 759px;
}

.group_7 {
  height: 230px;
  background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 1030px;
  position: relative;
  margin: 120px 0 0 443px;
}

.text-wrapper_5 {
  width: 409px;
  height: 22px;
  margin: 20px 0 0 60px;
}

.text_30 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: Alibaba-PuHuiTi-B;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text_31 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: Alibaba-PuHuiTi-B;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text_32 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: Alibaba-PuHuiTi-B;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text-wrapper_6 {
  width: 443px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_33 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTiR;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text_34 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTiR;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text_35 {
  width: 95px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTiR;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 112px;
}

.text-wrapper_7 {
  width: 63px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_36 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTiR;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text-wrapper_8 {
  width: 32px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_37 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTiR;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.block_2 {
  width: 179px;
  height: 55px;
  margin: 28px 0 3px 20px;
}

.image-text_1 {
  width: 179px;
  height: 55px;
}

.image_1 {
  width: 56px;
  height: 55px;
}

.text-group_1 {
  width: 119px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: Alibaba-PuHuiTi-M;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
  margin-top: 6px;
}

.block_3 {
  background-color: rgba(0, 0, 0, 1);
  position: absolute;
  left: 236px;
  top: 170px;
  width: 1241px;
  height: 60px;
}
